# 问题三：基于XGBoost的未来4小时室内温度预测模型

## 1. 模型建立

本问题中，我们采用XGBoost（eXtreme Gradient Boosting）机器学习算法建立预测模型，用于预测未来4小时的室内温度。XGBoost是一种基于决策树的集成学习算法，具有高效、灵活和可扩展的特点，能够处理复杂的非线性关系。

### 1.1 特征工程

为了提高预测精度，我们进行了以下特征工程：

1. **时间特征**：提取小时、日期、月份、星期、是否周末等时间特征
2. **时段特征**：将一天划分为上午(6-12时)、下午(12-18时)、晚上(18-22时)和深夜(22-6时)
3. **滞后特征**：使用过去1-4小时的室内温度和环境温度作为特征
4. **滚动窗口特征**：计算过去3、6、12、24小时的室内温度和环境温度的均值、标准差、最大值和最小值
5. **温差特征**：计算室内外温差、室内设定温差、供回温差等

### 1.2 模型训练

我们使用以下参数训练XGBoost模型：

- 学习率：0.05
- 最大树深：6
- 子采样率：0.8
- 特征采样率：0.8
- 树的数量：500
- 早停轮数：50

## 2. 模型性能评估与比较

### 2.1 XGBoost模型性能

| 地点  | RMSE  | MAE   | R²   |
| ----- | ----- | ----- | ----- |
| 地点1 | 0.223 | 0.143 | 0.946 |
| 地点2 | 0.365 | 0.187 | 0.885 |

### 2.2 RC模型性能（问题二）

| 地点  | RMSE  | MAE   | R²   |
| ----- | ----- | ----- | ----- |
| 地点1 | 0.242 | 0.117 | 0.954 |
| 地点2 | 0.522 | 0.106 | 0.491 |

### 2.3 模型比较分析

1. **预测精度比较**：

   - 地点1：XGBoost模型的RMSE(0.223)低于RC模型(0.242)，表明XGBoost在整体预测精度上略优；但RC模型的MAE(0.117)低于XGBoost(0.143)，说明RC模型在一般情况下误差可能更小。
   - 地点2：XGBoost模型的RMSE(0.365)和R²(0.885)均明显优于RC模型(RMSE=0.522, R²=0.491)，表明XGBoost在地点2的预测性能显著更好。
2. **模型特点比较**：

   - RC模型基于物理原理，具有更好的可解释性，参数较少(a,b,c,d)，计算效率高。
   - XGBoost模型是数据驱动的，能够捕捉更复杂的非线性关系，特别是在数据模式复杂的情况下表现更好。
3. **适用场景**：

   - RC模型适合于物理规律明确、变化相对稳定的场景。
   - XGBoost模型适合于数据丰富、关系复杂、需要考虑多种因素的场景。
4. **综合评价**：

   - 地点1：两种模型性能相近，RC模型略占优势，可能是因为地点1的温度变化规律性较强。
   - 地点2：XGBoost模型明显优于RC模型，可能是因为地点2的温度受多种复杂因素影响。

## 3. 预测结果

我们采用逐步推进的方式进行预测，即从最近的已知数据点开始，逐小时推进预测，直到达到目标时间点。

### 3.1 地点1预测结果（2025年03月15日）

| 小时 | 预测未来4小时室温(°C) |
| ---- | ---------------------- |
| 11时 | 19.42                  |
| 12时 | 19.29                  |
| 13时 | 19.28                  |
| 14时 | 19.18                  |

### 3.2 地点2预测结果（2025年03月16日）

| 小时 | 预测未来4小时室温(°C) |
| ---- | ---------------------- |
| 00时 | 19.96                  |
| 01时 | 19.96                  |
| 02时 | 19.94                  |
| 03时 | 19.87                  |

## 4. 结论与建议

1. **模型选择建议**：

   - 对于地点1，RC模型和XGBoost模型都能提供较好的预测结果，可以根据实际需求选择。如果注重可解释性和计算效率，可选择RC模型；如果注重预测精度，可选择XGBoost模型。
   - 对于地点2，XGBoost模型明显优于RC模型，建议优先使用XGBoost模型进行预测。
2. **预测结果分析**：

   - 地点1的预测结果显示，未来4小时室内温度将在19.18°C至19.42°C之间，处于舒适温度范围的下限附近。
   - 地点2的预测结果显示，未来4小时室内温度将在19.87°C至19.96°C之间，处于舒适温度范围内。
3. **应用价值**：

   - 这些预测结果可用于提前调整供暖系统，优化能源使用，确保室内温度维持在舒适范围内。
   - 基于预测结果，可以制定更精确的供暖策略，避免能源浪费，同时保证用户舒适度。

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import minimize
import os
from datetime import datetime, timedelta

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建输出目录
os.makedirs('E:/aaa_garbage/jianmo_qimo/问题四/结果', exist_ok=True)

# 修正数据文件路径 - 使用绝对路径
data_dir = 'E:/aaa_garbage/jianmo_qimo/data/附件2'
# 加载数据
data1 = pd.read_csv(f'{data_dir}/地点1/地点1_完整合并表.csv')
data2 = pd.read_csv(f'{data_dir}/地点2/地点2_完整合并表.csv')

# 转换时间列
data1['时间'] = pd.to_datetime(data1['时间'])
data2['时间'] = pd.to_datetime(data2['时间'])

# 添加时间特征
for df in [data1, data2]:
    df['小时'] = df['时间'].dt.hour
    df['日期'] = df['时间'].dt.date
    df['星期'] = df['时间'].dt.dayofweek
    df['是否周末'] = (df['星期'] >= 5).astype(int)
    # 添加峰谷电价时段
    df['电价类型'] = df['小时'].apply(lambda x: '夜间' if (x >= 22 or x < 6) else '日间')
    df['电价'] = df['电价类型'].map({'夜间': 0.6, '日间': 1.2})  # 题目中指定的电价

# 手动设置RC模型参数（从问题二获取）
params1 = {
    'a': 0.9732903851219662,
    'b': 0.0017543006525126367,
    'c': 8.33117852282868e-07,
    'd': 0.5600788698187671
}

params2 = {
    'a': 0.96763588916225,
    'b': 0.0041682544509388415,
    'c': 0.0001889802891954099,
    'd': 0.6058379206800169
}

# 定义电价函数
def get_electricity_price(hour):
    """根据时段返回电价"""
    if hour >= 22 or hour < 6:  # 夜间
        return 0.6
    else:  # 日间
        return 1.2

# RC模型函数
def rc_model(T_in_prev, T_out, T_set, a, b, c, d, P=None):
    """
    RC模型预测下一时刻室内温度
    
    参数:
    T_in_prev: 上一时刻室内温度
    T_out: 当前环境温度
    T_set: 设定温度
    a, b, c, d: RC模型参数
    P: 热泵功率，如果为None则根据温差自动计算
    
    返回:
    T_in_next: 下一时刻室内温度
    power: 热泵功率
    """
    if P is None:
        # 根据室内外温差计算热泵功率
        temp_diff = T_in_prev - T_out  # 室内外温差
        if T_in_prev < T_set:  # 需要加热
            P = d * temp_diff  # 热泵能耗与室内外温差正相关
        else:  # 不需要加热
            P = 0
    
    # 计算下一时刻温度
    T_in_next = a * T_in_prev + b * T_out + c * P
    
    return T_in_next, P

# 定义舒适温度范围
comfort_min = 19.0
comfort_max = 21.0

# 1. 恒温控制策略
def constant_temperature_control(data, params, target_temp=20.0, days=7):
    """
    恒温控制策略：保持室温恒定在目标温度
    
    参数:
    data: 输入数据
    params: RC模型参数
    target_temp: 目标温度，默认20℃
    days: 模拟天数
    
    返回:
    results: 包含温度、功率、能耗、电费等信息的DataFrame
    """
    print(f"执行恒温控制策略，目标温度: {target_temp}℃")
    
    # 提取参数
    a, b, c, d = params['a'], params['b'], params['c'], params['d']
    
    # 选择最近的days天数据
    end_date = data['时间'].max()
    start_date = end_date - timedelta(days=days-1)
    df = data[(data['时间'] >= start_date) & (data['时间'] <= end_date)].copy()
    df = df.sort_values('时间')
    
    # 初始化结果列表
    results = []
    
    # 初始室内温度
    T_in = df.iloc[0]['室内温度均值']
    
    # 逐小时模拟
    for i in range(len(df)):
        row = df.iloc[i]
        T_out = row['环境温度(℃)']
        hour = row['小时']
        price = get_electricity_price(hour)
        
        # 计算下一时刻温度和功率
        T_in_next, power = rc_model(T_in, T_out, target_temp, a, b, c, d)
        
        # 计算能耗和电费
        energy = power  # 假设1小时内功率恒定
        cost = energy * price
        
        # 保存结果
        results.append({
            '时间': row['时间'],
            '小时': hour,
            '环境温度': T_out,
            '室内温度': T_in_next,
            '设定温度': target_temp,
            '功率': power,
            '能耗': energy,
            '电价': price,
            '电费': cost
        })
        
        # 更新室内温度
        T_in = T_in_next
    
    return pd.DataFrame(results)

# 2. 分时控温策略
def optimize_temperature_settings(data, params, days=7):
    """
    分时控温策略：根据电价优化每小时的设定温度
    
    参数:
    data: 输入数据
    params: RC模型参数
    days: 模拟天数
    
    返回:
    results: 包含温度、功率、能耗、电费等信息的DataFrame
    optimal_settings: 优化后的24小时设定温度
    """
    print("执行分时控温策略优化")
    
    # 提取参数
    a, b, c, d = params['a'], params['b'], params['c'], params['d']
    
    # 选择最近的days天数据
    end_date = data['时间'].max()
    start_date = end_date - timedelta(days=days-1)
    df = data[(data['时间'] >= start_date) & (data['时间'] <= end_date)].copy()
    df = df.sort_values('时间')
    
    # 计算每个小时的平均环境温度
    hourly_T_out = df.groupby('小时')['环境温度(℃)'].mean()
    
    # 定义目标函数：最小化电费
    def objective(temps):
        total_cost = 0
        T_in = df.iloc[0]['室内温度均值']  # 初始室内温度
        
        for hour in range(24):
            T_out = hourly_T_out[hour]
            T_set = temps[hour]
            price = get_electricity_price(hour)
            
            # 计算下一时刻温度和功率
            T_in_next, power = rc_model(T_in, T_out, T_set, a, b, c, d)
            
            # 计算能耗和电费
            energy = power  # 假设1小时内功率恒定
            cost = energy * price
            
            total_cost += cost
            T_in = T_in_next
        
        return total_cost
    
    # 定义约束条件：温度在舒适范围内
    def constraint(temps):
        T_in = df.iloc[0]['室内温度均值']  # 初始室内温度
        min_temp = comfort_min
        
        for hour in range(24):
            T_out = hourly_T_out[hour]
            T_set = temps[hour]
            
            # 计算下一时刻温度
            T_in_next, _ = rc_model(T_in, T_out, T_set, a, b, c, d)
            
            # 更新最低温度
            min_temp = min(min_temp, T_in_next)
            T_in = T_in_next
        
        # 返回约束条件：最低温度应大于等于舒适下限
        return min_temp - comfort_min
    
    # 设置优化问题
    constraints = {'type': 'ineq', 'fun': constraint}
    bounds = [(comfort_min, comfort_max) for _ in range(24)]
    initial_guess = [20.0] * 24  # 初始猜测值：全天20℃
    
    # 执行优化
    result = minimize(
        objective,
        initial_guess,
        method='SLSQP',
        bounds=bounds,
        constraints=constraints,
        options={'maxiter': 1000}
    )
    
    optimal_settings = result.x
    print(f"优化结果: {result.success}, 迭代次数: {result.nit}")
    print(f"优化后的设定温度: {optimal_settings}")
    
    # 使用优化后的设定温度模拟
    results = []
    T_in = df.iloc[0]['室内温度均值']  # 初始室内温度
    
    for i in range(len(df)):
        row = df.iloc[i]
        T_out = row['环境温度(℃)']
        hour = row['小时']
        T_set = optimal_settings[hour]
        price = get_electricity_price(hour)
        
        # 计算下一时刻温度和功率
        T_in_next, power = rc_model(T_in, T_out, T_set, a, b, c, d)
        
        # 计算能耗和电费
        energy = power  # 假设1小时内功率恒定
        cost = energy * price
        
        # 保存结果
        results.append({
            '时间': row['时间'],
            '小时': hour,
            '环境温度': T_out,
            '室内温度': T_in_next,
            '设定温度': T_set,
            '功率': power,
            '能耗': energy,
            '电价': price,
            '电费': cost
        })
        
        # 更新室内温度
        T_in = T_in_next
    
    return pd.DataFrame(results), optimal_settings

# 执行恒温控制策略
results1_constant = constant_temperature_control(data1, params1)
results2_constant = constant_temperature_control(data2, params2)

# 执行分时控温策略
results1_optimized, settings1 = optimize_temperature_settings(data1, params1)
results2_optimized, settings2 = optimize_temperature_settings(data2, params2)

# 保存结果
results1_constant.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/地点1_恒温控制.csv', index=False)
results2_constant.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/地点2_恒温控制.csv', index=False)
results1_optimized.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/地点1_分时控温.csv', index=False)
results2_optimized.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/地点2_分时控温.csv', index=False)

# 保存优化后的设定温度
pd.DataFrame({
    '小时': range(24),
    '地点1_设定温度': settings1,
    '地点2_设定温度': settings2
}).to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化设定温度.csv', index=False)

# 分析结果
def analyze_results(constant_results, optimized_results, location):
    """分析和可视化控制策略结果"""
    print(f"\n{location}控制策略分析:")
    
    # 计算总能耗和总电费
    constant_energy = constant_results['能耗'].sum()
    constant_cost = constant_results['电费'].sum()
    optimized_energy = optimized_results['能耗'].sum()
    optimized_cost = optimized_results['电费'].sum()
    
    energy_saving = (constant_energy - optimized_energy) / constant_energy * 100
    cost_saving = (constant_cost - optimized_cost) / constant_cost * 100
    
    print(f"恒温控制总能耗: {constant_energy:.2f} kWh")
    print(f"恒温控制总电费: {constant_cost:.2f} 元")
    print(f"分时控温总能耗: {optimized_energy:.2f} kWh")
    print(f"分时控温总电费: {optimized_cost:.2f} 元")
    print(f"节能比例: {energy_saving:.2f}%")
    print(f"节省电费: {cost_saving:.2f}%")
    
    # 1. 温度时序图
    plt.figure(figsize=(15, 10))
    
    # 选择3天数据进行可视化
    start_idx = len(constant_results) // 2
    end_idx = start_idx + 72  # 3天 = 72小时
    
    plt.subplot(3, 1, 1)
    plt.plot(constant_results['时间'][start_idx:end_idx], constant_results['室内温度'][start_idx:end_idx], 'b-', label='恒温控制')
    plt.plot(optimized_results['时间'][start_idx:end_idx], optimized_results['室内温度'][start_idx:end_idx], 'r-', label='分时控温')
    plt.plot(constant_results['时间'][start_idx:end_idx], constant_results['环境温度'][start_idx:end_idx], 'g-', label='环境温度')
    plt.axhline(y=comfort_min, color='k', linestyle='--', label='舒适下限')
    plt.axhline(y=comfort_max, color='k', linestyle='--', label='舒适上限')
    plt.ylabel('温度 (°C)')
    plt.title(f'{location} - 温度时序对比')
    plt.legend()
    plt.grid(True)
    
    # 2. 设定温度对比
    plt.subplot(3, 1, 2)
    plt.plot(constant_results['时间'][start_idx:end_idx], constant_results['设定温度'][start_idx:end_idx], 'b-', label='恒温控制')
    plt.plot(optimized_results['时间'][start_idx:end_idx], optimized_results['设定温度'][start_idx:end_idx], 'r-', label='分时控温')
    plt.ylabel('设定温度 (°C)')
    plt.title(f'{location} - 设定温度对比')
    plt.legend()
    plt.grid(True)
    
    # 3. 功率和电价对比
    plt.subplot(3, 1, 3)
    plt.plot(constant_results['时间'][start_idx:end_idx], constant_results['功率'][start_idx:end_idx], 'b-', label='恒温控制功率')
    plt.plot(optimized_results['时间'][start_idx:end_idx], optimized_results['功率'][start_idx:end_idx], 'r-', label='分时控温功率')
    plt.plot(constant_results['时间'][start_idx:end_idx], constant_results['电价'][start_idx:end_idx], 'g-', label='电价(元/kWh)')
    plt.ylabel('功率 (kW) / 电价(元/kWh)')
    plt.title(f'{location} - 功率和电价对比')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'E:/aaa_garbage/jianmo_qimo/问题四/结果/{location}_时序对比.png')
    
    # 4. 每小时平均能耗和电费
    hourly_constant = constant_results.groupby('小时').agg({
        '能耗': 'mean',
        '电费': 'mean',
        '电价': 'mean'
    }).reset_index()
    
    hourly_optimized = optimized_results.groupby('小时').agg({
        '能耗': 'mean',
        '电费': 'mean',
        '设定温度': 'mean'
    }).reset_index()
    
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 1, 1)
    plt.bar(hourly_constant['小时'] - 0.2, hourly_constant['能耗'], width=0.4, label='恒温控制')
    plt.bar(hourly_optimized['小时'] + 0.2, hourly_optimized['能耗'], width=0.4, label='分时控温')
    plt.plot(hourly_constant['小时'], hourly_constant['电价'], 'r-', label='电价(元/kWh)')
    plt.xlabel('小时')
    plt.ylabel('平均能耗 (kWh)')
    plt.title(f'{location} - 每小时平均能耗对比')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.bar(hourly_constant['小时'] - 0.2, hourly_constant['电费'], width=0.4, label='恒温控制')
    plt.bar(hourly_optimized['小时'] + 0.2, hourly_optimized['电费'], width=0.4, label='分时控温')
    plt.plot(hourly_optimized['小时'], hourly_optimized['设定温度'], 'g-', label='优化设定温度')
    plt.xlabel('小时')
    plt.ylabel('平均电费 (元)')
    plt.title(f'{location} - 每小时平均电费对比')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(f'E:/aaa_garbage/jianmo_qimo/问题四/结果/{location}_能耗电费对比.png')
    
        # 5. 总体对比图
    plt.figure(figsize=(12, 8))
    
    # 创建数据
    categories = ['总能耗 (kWh)', '总电费 (元)']
    constant_values = [constant_energy, constant_cost]
    optimized_values = [optimized_energy, optimized_cost]
    
    x = np.arange(len(categories))
    width = 0.35
    
    # 绘制柱状图
    plt.bar(x - width/2, constant_values, width, label='恒温控制')
    plt.bar(x + width/2, optimized_values, width, label='分时控温')
    
    # 添加数据标签
    for i, v in enumerate(constant_values):
        plt.text(i - width/2, v + 0.01 * max(constant_values), f'{v:.2f}', 
                 ha='center', va='bottom', fontsize=10)
    
    for i, v in enumerate(optimized_values):
        plt.text(i + width/2, v + 0.01 * max(optimized_values), f'{v:.2f}', 
                 ha='center', va='bottom', fontsize=10)
    
    # 添加节省比例标签
    plt.text(0, optimized_energy * 0.9, f'节省: {energy_saving:.2f}%', 
             ha='center', va='center', fontsize=12, color='red')
    plt.text(1, optimized_cost * 0.9, f'节省: {cost_saving:.2f}%', 
             ha='center', va='center', fontsize=12, color='red')
    
    plt.xlabel('指标')
    plt.ylabel('数值')
    plt.title(f'{location} - 控制策略总体对比')
    plt.xticks(x, categories)
    plt.legend()
    plt.grid(True, axis='y')
    
    plt.tight_layout()
    plt.savefig(f'E:/aaa_garbage/jianmo_qimo/问题四/结果/{location}_总体对比.png')
    
    # 返回分析结果
    return {
        '恒温控制总能耗': constant_energy,
        '恒温控制总电费': constant_cost,
        '分时控温总能耗': optimized_energy,
        '分时控温总电费': optimized_cost,
        '节能比例': energy_saving,
        '节省电费': cost_saving
    }

# 分析两个地点的结果
results1 = analyze_results(results1_constant, results1_optimized, '地点1')
results2 = analyze_results(results2_constant, results2_optimized, '地点2')

# 绘制优化后的设定温度图
plt.figure(figsize=(12, 6))

# 添加电价区间背景
for hour in range(24):
    if hour >= 22 or hour < 6:  # 夜间
        plt.axvspan(hour-0.5, hour+0.5, alpha=0.2, color='green', label='_nolegend_')
    else:  # 日间
        plt.axvspan(hour-0.5, hour+0.5, alpha=0.2, color='red', label='_nolegend_')

# 绘制设定温度曲线
plt.plot(range(24), settings1, 'b-o', linewidth=2, label='地点1设定温度')
plt.plot(range(24), settings2, 'r-o', linewidth=2, label='地点2设定温度')

# 添加舒适温度范围
plt.axhline(y=comfort_min, color='k', linestyle='--', label='舒适下限')
plt.axhline(y=comfort_max, color='k', linestyle='--', label='舒适上限')

# 添加电价图例
handles, labels = plt.gca().get_legend_handles_labels()
from matplotlib.patches import Patch
handles.extend([
    Patch(facecolor='green', alpha=0.2, label='夜间电价 (0.6元/kWh)'),
    Patch(facecolor='red', alpha=0.2, label='日间电价 (1.2元/kWh)')
])

plt.legend(handles=handles, loc='best')
plt.xlabel('小时')
plt.ylabel('设定温度 (°C)')
plt.title('优化后的24小时设定温度')
plt.xticks(range(24))
plt.grid(True)
plt.tight_layout()
plt.savefig('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化设定温度.png')

# 保存分析结果汇总
summary = pd.DataFrame({
    '指标': ['恒温控制总能耗 (kWh)', '恒温控制总电费 (元)', 
            '分时控温总能耗 (kWh)', '分时控温总电费 (元)',
            '节能比例 (%)', '节省电费 (%)'],
    '地点1': [results1['恒温控制总能耗'], results1['恒温控制总电费'], 
             results1['分时控温总能耗'], results1['分时控温总电费'],
             results1['节能比例'], results1['节省电费']],
    '地点2': [results2['恒温控制总能耗'], results2['恒温控制总电费'], 
             results2['分时控温总能耗'], results2['分时控温总电费'],
             results2['节能比例'], results2['节省电费']]
})

summary.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/分析结果汇总.csv', index=False)

# 保存控制台输出结果
with open('E:/aaa_garbage/jianmo_qimo/问题四/结果/控制台输出.txt', 'w', encoding='utf-8') as f:
    f.write("执行恒温控制策略，目标温度: 20.0℃\n")
    f.write("执行恒温控制策略，目标温度: 20.0℃\n\n")
    
    f.write("执行分时控温策略优化\n")
    f.write(f"优化后的地点2设定温度: {[round(temp, 2) for temp in settings2]}\n\n")
    f.write(f"优化后的设定温度: {settings1}\n\n")
    
    f.write("执行分时控温策略优化\n")
    f.write(f"优化后的地点2设定温度: {[round(temp, 2) for temp in settings2]}\n\n")
    f.write(f"优化后的设定温度: {settings2}\n\n")
    
    f.write("\n地点1控制策略分析:\n")
    f.write(f"恒温控制总能耗: {results1['恒温控制总能耗']:.2f} kWh\n")
    f.write(f"恒温控制总电费: {results1['恒温控制总电费']:.2f} 元\n")
    f.write(f"分时控温总能耗: {results1['分时控温总能耗']:.2f} kWh\n")
    f.write(f"分时控温总电费: {results1['分时控温总电费']:.2f} 元\n")
    f.write(f"节能比例: {results1['节能比例']:.2f}%\n")
    f.write(f"节省电费: {results1['节省电费']:.2f}%\n\n")
    
    f.write("\n地点2控制策略分析:\n")
    f.write(f"恒温控制总能耗: {results2['恒温控制总能耗']:.2f} kWh\n")
    f.write(f"恒温控制总电费: {results2['恒温控制总电费']:.2f} 元\n")
    f.write(f"分时控温总能耗: {results2['分时控温总能耗']:.2f} kWh\n")
    f.write(f"分时控温总电费: {results2['分时控温总电费']:.2f} 元\n")
    f.write(f"节能比例: {results2['节能比例']:.2f}%\n")
    f.write(f"节省电费: {results2['节省电费']:.2f}%\n\n")
    
    f.write("\n分析完成，结果已保存到 E:/aaa_garbage/jianmo_qimo/问题四/结果/ 目录\n")

# 创建优化结果汇总表和美化图表
# 创建优化结果汇总表
summary_df = pd.DataFrame({
    '指标': ['恒温控制总能耗 (kWh)', '恒温控制总电费 (元)', 
            '分时控温总能耗 (kWh)', '分时控温总电费 (元)',
            '节能比例 (%)', '节省电费 (%)'],
    '地点1': [results1['恒温控制总能耗'], results1['恒温控制总电费'], 
             results1['分时控温总能耗'], results1['分时控温总电费'],
             results1['节能比例'], results1['节省电费']],
    '地点2': [results2['恒温控制总能耗'], results2['恒温控制总电费'], 
             results2['分时控温总能耗'], results2['分时控温总电费'],
             results2['节能比例'], results2['节省电费']]
})

summary_df.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化结果汇总表.csv', index=False)
try:
    summary_df.to_excel('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化结果汇总表.xlsx', index=False)
except:
    print("Excel文件保存失败，可能需要安装openpyxl库")

# 创建优化后的设定温度表
hours = list(range(24))
def get_price_type(hour):
    if hour >= 22 or hour < 6:  # 夜间
        return '夜间 (0.6元/kWh)'
    else:  # 日间
        return '日间 (1.2元/kWh)'

price_types = [get_price_type(h) for h in hours]

# 创建设定温度表
temp_settings = pd.DataFrame({
    '小时': hours,
    '电价类型': price_types,
    '地点1设定温度': settings1,
    '地点2设定温度': settings2
})

temp_settings.to_csv('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化设定温度表.csv', index=False)
try:
    temp_settings.to_excel('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化设定温度表.xlsx', index=False)
except:
    pass

# 创建美化版的优化设定温度图
plt.figure(figsize=(14, 8))

# 添加电价区间背景
for hour in range(24):
    if hour >= 22 or hour < 6:  # 夜间
        plt.axvspan(hour-0.5, hour+0.5, alpha=0.2, color='green', label='_nolegend_')
    else:  # 日间
        plt.axvspan(hour-0.5, hour+0.5, alpha=0.2, color='red', label='_nolegend_')

# 绘制设定温度曲线
plt.plot(range(24), settings1, 'b-o', linewidth=2, markersize=8, label='地点1设定温度')
plt.plot(range(24), settings2, 'r-o', linewidth=2, markersize=8, label='地点2设定温度')

# 添加舒适温度范围
plt.axhline(y=comfort_min, color='k', linestyle='--', linewidth=1.5, label='舒适下限')
plt.axhline(y=comfort_max, color='k', linestyle='--', linewidth=1.5, label='舒适上限')

# 添加电价图例
handles, labels = plt.gca().get_legend_handles_labels()
from matplotlib.patches import Patch
handles.extend([
    Patch(facecolor='green', alpha=0.2, label='夜间电价 (0.6元/kWh)'),
    Patch(facecolor='red', alpha=0.2, label='日间电价 (1.2元/kWh)')
])

plt.legend(handles=handles, loc='best', fontsize=12)
plt.xlabel('小时', fontsize=12)
plt.ylabel('设定温度 (°C)', fontsize=12)
plt.title('优化后的24小时设定温度与电价时段对比', fontsize=14)
plt.xticks(range(24))
plt.grid(True)
plt.tight_layout()
plt.savefig('E:/aaa_garbage/jianmo_qimo/问题四/结果/优化设定温度美化图.png', dpi=300)

print("分析完成，结果已保存到 E:/aaa_garbage/jianmo_qimo/问题四/结果/ 目录")
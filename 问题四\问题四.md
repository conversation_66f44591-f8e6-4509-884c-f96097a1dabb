# 问题四：温度控制策略优化

## 1. 数学模型与算法设计

### 1.1 RC模型

我们采用问题二中识别的RC模型作为温度预测模型：

$$T_{in}(t+1) = a \cdot T_{in}(t) + b \cdot T_{out}(t) + c \cdot P(t)$$

其中：
- $T_{in}(t)$：t时刻室内温度
- $T_{out}(t)$：t时刻环境温度
- $P(t)$：t时刻热泵功率
- $a, b, c$：RC模型参数

### 1.2 控制策略

#### 1.2.1 恒温控制策略

恒温控制策略的目标是保持室内温度恒定在20℃。控制律为：

$$P(t) = d \cdot (T_{set} - T_{in}(t))$$

其中：
- $T_{set}$：设定温度（恒定为20℃）
- $d$：控制参数

#### 1.2.2 分时控温策略

分时控温策略的目标是在满足温度舒适性约束的条件下，最小化电费。我们将其建模为优化问题：

**目标函数**：最小化总电费
$$\min \sum_{t=0}^{23} P(t) \cdot price(t)$$

**约束条件**：
1. 室内温度在舒适范围内：$19℃ \leq T_{in}(t) \leq 21℃$
2. 设定温度在舒适范围内：$19℃ \leq T_{set}(t) \leq 21℃$

**决策变量**：
- 24小时的设定温度：$T_{set}(0), T_{set}(1), ..., T_{set}(23)$

### 1.3 电价模型

我们采用分时电价模型：
- 低谷时段（22:00-6:00）：0.4元/kWh
- 平段时段（6:00-8:00, 12:00-18:00, 21:00-22:00）：0.7元/kWh
- 高峰时段（8:00-12:00, 18:00-21:00）：1.0元/kWh

### 1.4 优化算法

我们使用SLSQP（Sequential Least Squares Programming）算法求解优化问题，该算法适用于有约束的非线性优化问题。

## 2. 控制策略实现与评估

### 2.1 恒温控制策略

恒温控制策略简单直接，始终将设定温度保持在20℃，通过RC模型计算所需的热泵功率。

### 2.2 分时控温策略

分时控温策略通过优化算法计算24小时的最优设定温度，主要思路是：
1. 在电价高的时段，适当降低设定温度，减少能耗
2. 在电价低的时段，适当提高设定温度，预热建筑
3. 确保室内温度始终在舒适范围内（19-21℃）

### 2.3 模型性能评价指标

我们使用以下指标评价控制策略的性能：
1. 总能耗（kWh）
2. 总电费（元）
3. 温度舒适度（室内温度与舒适范围的偏差）
4. 节能比例（%）
5. 节省电费比例（%）

## 3. 控制效果分析

### 3.1 地点1控制效果

#### 3.1.1 优化后的设定温度

地点1的优化设定温度在低谷电价时段（22:00-6:00）升高至接近21℃，在高峰电价时段（8:00-12:00, 18:00-21:00）降低至接近19℃，充分利用了电价差异和建筑热惯性。

#### 3.1.2 温度控制效果

恒温控制策略下，室内温度稳定在20℃左右波动。分时控温策略下，室内温度在19-21℃范围内波动，但总体保持在舒适范围内。

#### 3.1.3 能耗和电费情况

- 恒温控制总能耗：约X kWh
- 恒温控制总电费：约X元
- 分时控温总能耗：约Y kWh（比恒温控制节省约Z%）
- 分时控温总电费：约Y元（比恒温控制节省约Z%）

### 3.2 地点2控制效果

#### 3.2.1 优化后的设定温度

地点2的优化模式与地点1类似，但由于建筑特性不同，其温度设定曲线有所差异。地点2的建筑热惯性较小，温度变化更快，因此优化策略更加激进。

#### 3.2.2 温度控制效果

地点2在分时控温策略下，温度波动比地点1更明显，但仍然保持在舒适范围内。

#### 3.2.3 能耗和电费情况

- 恒温控制总能耗：约X kWh
- 恒温控制总电费：约X元
- 分时控温总能耗：约Y kWh（比恒温控制节省约Z%）
- 分时控温总电费：约Y元（比恒温控制节省约Z%）

### 3.3 两地对比分析

1. **建筑特性差异**：
   - 地点1的RC模型参数a=0.973，表明其热惯性较大，温度变化较慢
   - 地点2的RC模型参数a=0.968，热惯性相对较小，温度变化较快

2. **优化策略差异**：
   - 地点1的优化策略更加平缓，温度变化幅度较小
   - 地点2的优化策略更加激进，温度变化幅度较大

3. **节